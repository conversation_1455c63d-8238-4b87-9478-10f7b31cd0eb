import { createRouter, createWebHistory } from 'vue-router'
import Layout from '../components/Layout.vue'
import OpeningBids from '../components/OpeningBids.vue'
import Responses from '../components/Responses.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: Layout,
      children: [
        {
          path: '',
          redirect: '/opening'
        },
        {
          path: '/opening',
          component: OpeningBids
        },
        {
          path: '/responses',
          component: Responses
        }
        // 其他路由将在后续添加
      ]
    }
  ]
})

export default router 