<template>
  <div class="bridge-card">
    <h2 class="bridge-title">第一章 1♣️开叫及以后的应叫</h2>

    <!-- 1C应叫主表格 -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—? 应叫表</h3>
      <el-table :data="oneClubResponsesFull" style="width: 100%" border>
        <el-table-column prop="bid" label="应叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.bid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="牌型/说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 注意事项卡片 -->
    <div class="bridge-card mb-8">
      <h3 class="bridge-subtitle">注意事项（第3~4家开叫1♣️）</h3>
      <div class="prose max-w-none">
        <ul class="list-disc pl-6">
          <li>1♦️：6-11点，保持原意，不逼叫。</li>
          <li>1♥️/1♠️：6-11点，保持原意，不逼叫。</li>
          <li>2♣️：8-11点，4张以上♣️，没有4张高花，不逼叫。</li>
        </ul>
      </div>
    </div>

    <!-- 1C—1D后开叫方的再叫（主表格） -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1♦️ 后开叫方的再叫</h3>
      <el-table :data="oneClubOneDiamondRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1D—1H后应叫方的再叫 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—1♦️—1♥️ 后应叫方的再叫</h4>
      <el-table :data="oneClubOneDiamondOneHeartRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1D—1S后应叫方的再叫 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—1♦️—1♠️ 后应叫方的再叫</h4>
      <el-table :data="oneClubOneDiamondOneSpadeRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C-1H/1S后开叫方的再叫表格 -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1♥️/1♠️ 后开叫方的再叫</h3>
      <el-table :data="oneClubOneMajorRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1D—1NT后应叫方的再叫（主表格） -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1♦️—1NT 后应叫方的再叫（双路重询斯台曼）</h3>
      <el-table :data="oneClubOneDiamondOneNTRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1D—1NT—2C后再叫 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—1♦️—1NT—2♣️ 后再叫</h4>
      <el-table :data="oneClubOneDiamondOneNTTwoClubRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1D—1NT—2D后再叫 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—1♦️—1NT—2♦️ 后再叫</h4>
      <el-table :data="oneClubOneDiamondOneNTTwoDiamondRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新低花逼局体系 1C—1H—2C—2D—? -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1♥️—2♣️—2♦️（新低花逼局，与♦️无关）</h3>
      <el-table :data="oneClubOneHeartTwoClubTwoDiamondRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新低花逼局体系 1C—1S—2C—2D—? -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1♠️—2♣️—2♦️（新低花逼局，与♦️无关）</h3>
      <el-table :data="oneClubOneSpadeTwoClubTwoDiamondRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—1NT后开叫方的再叫体系 -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—1NT 后开叫方的再叫</h3>
      <el-table :data="oneClubOneNTRebid" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—2C后开叫方的再叫体系 -->
    <div class="mb-8">
      <h3 class="bridge-subtitle">1♣️—2♣️ 后开叫方的再叫</h3>
      <el-table :data="oneClubTwoClubRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—2C—2D后应叫方的再叫体系 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—2♣️—2♦️ 后应叫方的再叫</h4>
      <el-table :data="oneClubTwoClubTwoDiamondRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—2C—2H后应叫方的再叫体系 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—2♣️—2♥️ 后应叫方的再叫</h4>
      <el-table :data="oneClubTwoClubTwoHeartRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—2C—2S后应叫方的再叫体系 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—2♣️—2♠️ 后应叫方的再叫</h4>
      <el-table :data="oneClubTwoClubTwoSpadeRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 1C—2C—2NT后应叫方的再叫体系 -->
    <div class="mb-8">
      <h4 class="bridge-subtitle">1♣️—2♣️—2NT 后应叫方的再叫</h4>
      <el-table :data="oneClubTwoClubTwoNTRebidFull" style="width: 100%" border>
        <el-table-column prop="rebid" label="再叫" width="120">
          <template #default="{ row }">
            <span class="font-bold text-bridge-blue">{{ suitToEmoji(row.rebid) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="点力范围" width="120">
          <template #default="{ row }">
            <el-tag :type="getPointTagType(row.points)" effect="plain">{{ row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明">
          <template #default="{ row }">
            <span>{{ suitToEmoji(row.description) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 详细说明、要点、问答等 -->
    <div class="bridge-card">
      <h3 class="bridge-subtitle">体系要点 & 常见问答</h3>
      <div class="prose max-w-none">
        <ul class="list-disc pl-6 mb-4">
          <li>应叫需根据自身牌力和花色长度合理选择，避免过度冒险。</li>
          <li>逼叫和阻击叫要结合开叫方的牌型和点力综合判断。</li>
          <li>反加叫和邀叫需注意后续体系衔接。</li>
        </ul>
        <ul class="list-disc pl-6">
          <li>Q: 什么时候选择1NT应叫？<br/>A: 没有4张高花且点力适中时。</li>
          <li>Q: 反加叫和阻击叫的区别？<br/>A: 反加叫为进攻性，阻击叫为扰乱对手。</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

function suitToEmoji(str: string) {
  return str
    .replace(/C/g, '♣️')
    .replace(/D/g, '♦️')
    .replace(/H/g, '♥️')
    .replace(/S/g, '♠️')
}

const oneClubResponsesFull = ref([
  { bid: 'Pass', points: '0-5点', description: '4~5张以上C，弱牌' },
  { bid: '1D', points: '6-11点', description: '4张D↑，无4张高花且不适合1NT；或12点以上，5张D↑，D长于H/S，逼叫' },
  { bid: '1H/1S', points: '6-11点', description: '4张H/S↑，或有更长低花；或12点↑，H/S为长套，4-4M叫1H，5-5M叫1S，逼叫' },
  { bid: '1NT', points: '6-10点', description: '没有4张高花，但可能有4~5张C，不逼叫' },
  { bid: '2C', points: '10点以上', description: '4张以上C，低花反加叫，逼叫' },
  { bid: '2D', points: '4-8点', description: '6张以上D，阻击叫' },
  { bid: '2H/2S', points: '4-8点', description: '6张以上H/S，阻击叫' },
  { bid: '2NT', points: '11-12点', description: '没有4张高花和5张以上C，邀叫' },
  { bid: '3C', points: '4-8点', description: '5张以上C，无4张高花，阻击叫；6-8点时通常高花有单缺（否则叫1NT），不逼叫' },
  { bid: '3D', points: '12-15点', description: '5张以上C，没有4张高花，D单缺，SPL，逼局' },
  { bid: '3H/3S', points: '12-15点', description: '5张以上C，没有4张高花，H/S单缺，SPL，逼局' },
  { bid: '3NT', points: '13-15点', description: '没有4张高花' },
  { bid: '4C', points: '4-8点', description: '6张以上C，加深阻击；通常点力不高有牌型，不逼叫' },
  { bid: '4D/4H/4S', points: '5-8点', description: '7张以上D/H/S套' },
  { bid: '4NT', points: '18点以上', description: 'C为将牌的罗马关键张问叫' },
  { bid: '5C', points: '4-8点', description: '7张以上C；或6张C畸形牌' },
])

const oneClubOneDiamondRebidFull = ref([
  { rebid: '1H', points: '12-17点', description: '5张以上C＋4张H；或4-4-1-4牌型，不逼叫' },
  { rebid: '1S', points: '12-17点', description: '5张以上C＋4张S，不逼叫' },
  { rebid: '1NT', points: '12-14点', description: '3张以上C，均型，可能有4张高花，不逼叫' },
  { rebid: '2C', points: '12-15点', description: '6张以上C，没有4张高花，不逼叫' },
  { rebid: '2D', points: '12-15点', description: '5张以上C＋4张D支持，不逼叫' },
  { rebid: '2H/2S', points: '18-21点', description: '5张以上C＋4张H/S，跳叫新花逼局' },
  { rebid: '2NT', points: '18-19点', description: '3张以上C，可能有4张高花，不逼叫' },
  { rebid: '3C', points: '16-18点', description: '6张以上C，没有4张高花，跳叫原花邀叫' },
  { rebid: '3D', points: '16-18点', description: '5张以上C＋4张D支持，不逼叫' },
  { rebid: '3H/3S', points: '18-21点', description: '5张以上C＋4张D支持，所叫花色H/S单缺，SPL，逼局' },
  { rebid: '3NT', points: '16-18点', description: '坚固的6张以上C，高花有止张' },
])

const oneClubOneDiamondOneHeartRebid = ref([
  { rebid: 'Pass', points: '6-7点', description: '3张H，愿意打4-3配合' },
  { rebid: '1S', points: '12点以上', description: '第四花色逼局；与S无关' },
  { rebid: '1NT', points: '6-10点', description: '没有4张高花，不逼叫' },
  { rebid: '2C', points: '6-9点', description: '4张以上C，无高花，不逼叫' },
  { rebid: '2D', points: '6-9点', description: '6张以上D，不逼叫' },
  { rebid: '2H', points: '8-10点', description: '3张H，愿意打4-3配合' },
  { rebid: '2S', points: '12点以上', description: '5张D↑＋4张S，逼局' },
  { rebid: '2NT', points: '11-12点', description: '均型，高花无配合，邀叫' },
  { rebid: '3C', points: '11-12点', description: '4张以上C，邀叫' },
  { rebid: '3D', points: '11-12点', description: '6张以上D，邀叫' },
  { rebid: '3H', points: '15点以上', description: '5张D↑＋4张H，逼局' },
  { rebid: '3S/4C', points: '12点以上', description: '5张D↑＋4张H，SPL，逼局' },
  { rebid: '3NT', points: '13-15点', description: '止叫' },
  { rebid: '4D', points: '13点以上', description: '7张以上D，逼局' },
  { rebid: '4H', points: '12-14点', description: '5张D↑＋4张H，止叫' },
])

const oneClubOneDiamondOneSpadeRebid = ref([
  { rebid: 'Pass', points: '6-7点', description: '3张S，愿意打4-3配合' },
  { rebid: '1NT', points: '6-10点', description: '没有4张高花，不逼叫' },
  { rebid: '2C', points: '6-9点', description: '4张以上C，无高花，不逼叫' },
  { rebid: '2D', points: '6-9点', description: '6张以上D，不逼叫' },
  { rebid: '2H', points: '12点以上', description: '第四花色逼局；与H无关' },
  { rebid: '2S', points: '8-10点', description: '3张S，愿意打4-3配合' },
  { rebid: '2NT', points: '11-12点', description: '均型，高花无配合，邀叫' },
  { rebid: '3C', points: '11-12点', description: '4张以上C，邀叫' },
  { rebid: '3D', points: '11-12点', description: '6张以上D，邀叫' },
  { rebid: '3H', points: '12点以上', description: '5张D↑＋4张S，SPL，逼局' },
  { rebid: '3S', points: '15点以上', description: '5张D↑＋4张S，逼局' },
  { rebid: '3NT', points: '13-15点', description: '止叫' },
  { rebid: '4D', points: '13点以上', description: '7张以上D，逼局' },
  { rebid: '4S', points: '12-14点', description: '5张D↑＋4张S，止叫' },
])

const oneClubOneMajorRebid = ref([
  { rebid: '1S', points: '12-17点', description: '4张S，均型牌' },
  { rebid: '1NT', points: '12-14点', description: '均型牌，开叫方无4张高花' },
  { rebid: '2C/2D', points: '15-17点', description: '均型牌，开叫方有4张C/D' },
  { rebid: '2H/2S', points: '15-17点', description: '开叫方有4张H/S' },
  { rebid: '2NT', points: '18-19点', description: '均型牌，邀请满贯' },
  { rebid: '3NT', points: '20-21点', description: '均型牌，定约' },
  { rebid: '3C/3D', points: '15-17点', description: '5张以上C/D套，进攻性' },
  { rebid: '3H/3S', points: '15-17点', description: '5张以上H/S套，进攻性' },
  { rebid: '4C/4D', points: '18-21点', description: '6张以上C/D套，进攻性' },
  { rebid: '4H/4S', points: '18-21点', description: '6张以上H/S套，进攻性' },
])

const oneClubOneDiamondOneNTRebid = ref([
  { rebid: 'Pass', points: '6-9点', description: '不符合其他叫品' },
  { rebid: '2C', points: '6-9点', description: '要求同伴叫2D；后续Pass = 5张以上D，示弱' },
  { rebid: '2D', points: '10-12点', description: '要求同伴叫2D；后续选择自然叫品，邀叫' },
  { rebid: '2H', points: '13点以上', description: '不符合其他叫品，逼局' },
  { rebid: '2S', points: '13点以上', description: '5张以上D＋4张H，逼局' },
  { rebid: '2NT', points: '13点以上', description: '5张以上D＋4张S，逼局' },
  { rebid: '3C', points: '10-12点', description: '5张D＋4张C，不逼叫' },
  { rebid: '3D', points: '13点以上', description: '5张D＋5张C，逼局' },
  { rebid: '3H', points: '13点以上', description: '6张以上D，逼局' },
  { rebid: '3S', points: '13点以上', description: '5张D＋4张C，H单缺，逼局' },
  { rebid: '3NT', points: '13-17点', description: '不满足其他进局条件，止叫' },
  { rebid: '4NT', points: '18-19点', description: '满贯邀叫' },
])

const oneClubOneDiamondOneNTTwoClubRebid = ref([
  { rebid: '2D', points: '6-9点', description: '5张以上D，示弱止叫' },
  { rebid: '2NT', points: '10-12点', description: '5张D，均型牌，邀叫' },
  { rebid: '3C', points: '10-12点', description: '5张以上D＋5张C，邀叫' },
  { rebid: '3D', points: '10-12点', description: '好的6张以上D，邀叫' },
])

const oneClubOneDiamondOneNTTwoDiamondRebid = ref([
  { rebid: '2H', points: '12-14点', description: 'H有止张，S无止张' },
  { rebid: '2S', points: '12-14点', description: 'S有止张，H无止张' },
  { rebid: '2NT', points: '12-14点', description: 'S/H均有止张' },
  { rebid: '3C', points: '12-14点', description: '5张以上C' },
  { rebid: '3D', points: '12-14点', description: '带两大牌的3张D' },
])

const oneClubOneHeartTwoClubTwoDiamondRebid = ref([
  { rebid: '2H', points: '12-15点', description: '6张C＋3张H' },
  { rebid: '2S', points: '12-15点', description: '6张C，无3张H，S有止张' },
  { rebid: '2NT', points: '12-15点', description: '6张C，无3张H，未叫花色有止张' },
  { rebid: '3C', points: '12-15点', description: '6张以上C，不符合其他叫品' },
  { rebid: '3D', points: '12-15点', description: '6张C＋4张D，无3张H' },
  { rebid: '3H', points: '14-15点', description: '7张以上C，无3张H，D单缺' },
  { rebid: '3S', points: '14-15点', description: '7张以上C，无3张H，S单缺' },
])

const oneClubOneSpadeTwoClubTwoDiamondRebid = ref([
  { rebid: '2H', points: '12-15点', description: '6张C＋4张H' },
  { rebid: '2S', points: '12-15点', description: '6张C＋3张S；无4张H' },
  { rebid: '2NT', points: '12-15点', description: '无4张H，无3张S，其他有止' },
  { rebid: '3C', points: '12-15点', description: '6张以上C，不符合其他叫品' },
  { rebid: '3D', points: '12-15点', description: '6张C＋4张D，无3张S' },
  { rebid: '3H', points: '14-15点', description: '7张以上C，无3张S，H单缺' },
  { rebid: '3S', points: '14-15点', description: '7张以上C，无3张S，D单缺' },
])

const oneClubOneNTRebid = ref([
  { rebid: 'Pass', points: '12-14点', description: '不符合其他叫品' },
  { rebid: '2C', points: '12-15点', description: '5张以上C，不逼叫' },
  { rebid: '2D', points: '16-21点', description: '5张以上C＋4张D，逆叫，逼叫' },
  { rebid: '2H', points: '16-21点', description: '5张以上C＋4张H，逆叫，逼叫' },
  { rebid: '2S', points: '16-21点', description: '5张以上C＋4张S，逆叫，逼叫' },
  { rebid: '2NT', points: '16-18点', description: '不适合1NT开叫的牌型，邀叫' },
  { rebid: '3C', points: '16-18点', description: '6张以上C，无4张高花，邀叫' },
  { rebid: '3D', points: '16-21点', description: '6张以上C＋5张以上D，逼局' },
  { rebid: '3H', points: '16-21点', description: '6张以上C＋5张以上H，逼局' },
  { rebid: '3S', points: '16-21点', description: '6张以上C＋5张以上S，逼局' },
  { rebid: '3NT', points: '16-21点', description: '止叫' },
])

const oneClubTwoClubRebidFull = ref([
  { rebid: '2D', points: '12-21点', description: '5张C＋4张D，非均型，逼叫' },
  { rebid: '2H', points: '12-21点', description: '5张C＋4张H；或4-4-1-4牌型，非均型，逼叫' },
  { rebid: '2S', points: '12-21点', description: '5张C＋4张S，非均型，逼叫' },
  { rebid: '2NT', points: '12-14点', description: '均型，可以有4张高花，逼叫' },
  { rebid: '2NT', points: '18-19点', description: '均型，可以有4张高花；应叫人如果叫3NT就叫4NT邀请满贯' },
  { rebid: '3C', points: '12-14点', description: '4张以上C，高花有单缺，不逼叫' },
  { rebid: '3D', points: '14点以上', description: '4张以上C，D单缺，SPL，逼局' },
  { rebid: '3H/3S', points: '14点以上', description: '4张以上C，H/S单缺，SPL，逼局' },
])

const oneClubTwoClubTwoDiamondRebidFull = ref([
  { rebid: '2H', points: '10点以上', description: 'H有止张，S无止张，逼叫' },
  { rebid: '2S', points: '10点以上', description: 'S有止张，H无止张，逼叫' },
  { rebid: '2NT', points: '13点以上', description: '高花均有止张，逼局' },
  { rebid: '3C', points: '10-12点', description: '5张以上C，不逼叫' },
  { rebid: '3H/3S', points: '13点以上', description: '4张以上C，SPL，逼局' },
  { rebid: '4C', points: '10-12点', description: '5张以上C，M无废点，邀叫' },
])

const oneClubTwoClubTwoHeartRebidFull = ref([
  { rebid: '2S', points: '12点以上', description: 'S有止张或4张S，逼局' },
  { rebid: '2NT', points: '13点以上', description: 'D和S均有止张，逼局' },
  { rebid: '3C', points: '10-12点', description: '5张以上C，无4张M，不逼叫' },
  { rebid: '3D', points: '13点以上', description: 'D有止张或4张D，S无止，逼局' },
  { rebid: '3H', points: '14点以上', description: '5张C＋4张H，2-4-2-5型，高限' },
  { rebid: '3S', points: '13点以上', description: '5张C↑＋4张H，SPL，逼局' },
  { rebid: '4C', points: '10-12点', description: '5张以上C，M无废点，邀叫' },
  { rebid: '4D', points: '13点以上', description: '5张C↑＋4张H，SPL，逼局' },
  { rebid: '4H', points: '12-13点', description: '5张C＋4张H，2-4-2-5型，低限' },
])

const oneClubTwoClubTwoSpadeRebidFull = ref([
  { rebid: '2NT', points: '13点以上', description: 'D和H均有止张，逼局' },
  { rebid: '3C', points: '10-12点', description: '5张以上C，无4张M，不逼叫' },
  { rebid: '3D', points: '13点以上', description: 'D有止或4张D，H无止，逼局' },
  { rebid: '3H', points: '13点以上', description: 'D无止张，H有止张，逼局' },
  { rebid: '3S', points: '14点以上', description: '5张C＋4张S，4-2-2-5，高限' },
  { rebid: '4C', points: '10-12点', description: '5张以上C，M无废点，邀叫' },
  { rebid: '4D', points: '13点以上', description: '5张C↑＋4张S，SPL，逼局' },
  { rebid: '4H', points: '13点以上', description: '5张C↑＋4张S，SPL，逼局' },
  { rebid: '4S', points: '12-13点', description: '5张C＋4张S，4-2-2-5，低限' },
])

const oneClubTwoClubTwoNTRebidFull = ref([
  { rebid: '3C', points: '10-12点', description: '5张以上C，无4张M，不逼叫' },
  { rebid: '3D', points: '13点以上', description: 'D有止或4张，M有单缺，逼局' },
  { rebid: '3H/3S', points: '13点以上', description: '5张以上C＋4张H/S，逼局' },
  { rebid: '3NT', points: '12-15点', description: '没有4张高花，止叫' },
  { rebid: '4C', points: '10-12点', description: '5张C↑，未叫花色有单缺，邀叫' },
  { rebid: '4D/4H/4S', points: '16点以上', description: '5张C↑，SPL，强烈满贯兴趣' },
  { rebid: '4NT', points: '18-19点', description: '4张C↑，无4张高花，满贯邀叫' },
])

const getPointTagType = (points: string) => {
  if (points.includes('10点以上') || points.includes('11-12') || points.includes('18点以上') || points.includes('18-19') || points.includes('20-21')) return 'success'
  if (points.includes('6-11') || points.includes('6-10') || points.includes('12-14') || points.includes('12-15') || points.includes('13-15') || points.includes('15-17') || points.includes('5-8')) return 'info'
  if (points.includes('0-5') || points.includes('4-8')) return 'warning'
  return 'default'
}
</script>

<style scoped>
.prose {
  @apply text-gray-700 leading-relaxed;
}

.prose p {
  @apply mb-4;
}
</style> 