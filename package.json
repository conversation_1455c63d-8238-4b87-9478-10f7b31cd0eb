{"name": "bridge-system", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false"}, "dependencies": {"element-plus": "^2.6.1", "@element-plus/icons-vue": "^2.3.1", "vue": "^3.4.21", "vue-router": "^4.3.0"}, "devDependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "@tsconfig/node20": "^20.1.2", "@types/node": "^20.11.25", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^9.8.8", "npm-run-all2": "^6.1.2", "postcss": "^7.0.39", "tailwindcss": "npm:@tailwindcss/postcss7-compat", "typescript": "~5.4.0", "vite": "^5.1.5", "vue-tsc": "^2.0.6"}}